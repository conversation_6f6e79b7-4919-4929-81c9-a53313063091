import { useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  InputNumber,
  Checkbox,
  Row,
  Col,
  Space,
  Cascader,
  Divider,
  Button,
} from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
////
import { courseJSON, salaryJSON, timesJSON } from '@/hooks/useJSON';
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';
import { filterOption } from '@/utils/function';
import { useUserOptions, useZoomOptions } from '@/hooks/useApiOptions';
import { initFormDate, dayjs } from '@/hooks/useDayjs';

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const CheckboxOptions = [
  { value: 0, label: '周日' },
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
];

// Component for managing multiple time slots per weekday
const WeeklyTimeSlotManager: React.FC<{
  weekday: number;
  weekdayLabel: string;
  value?: API.TimeSlot[];
  onChange?: (timeSlots: API.TimeSlot[]) => void;
}> = ({ weekday, weekdayLabel, value = [], onChange }) => {
  const handleTimeSlotChange = (index: number, field: 'start_time' | 'end_time', time: string) => {
    const newTimeSlots = [...value];
    if (!newTimeSlots[index]) {
      newTimeSlots[index] = { start_time: '', end_time: '' };
    }
    newTimeSlots[index][field] = time;
    onChange?.(newTimeSlots);
  };

  const addTimeSlot = () => {
    const newTimeSlots = [...value, { start_time: '', end_time: '' }];
    onChange?.(newTimeSlots);
  };

  const removeTimeSlot = (index: number) => {
    const newTimeSlots = value.filter((_, i) => i !== index);
    onChange?.(newTimeSlots);
  };

  return (
    <div style={{ marginBottom: 16, border: '1px solid #d9d9d9', padding: 12, borderRadius: 6 }}>
      <div style={{ marginBottom: 8, fontWeight: 'bold' }}>{weekdayLabel}</div>
      {value.map((timeSlot, index) => (
        <Row key={index} gutter={8} style={{ marginBottom: 8 }}>
          <Col span={8}>
            <Select
              placeholder="开始时间"
              value={timeSlot.start_time}
              onChange={(time) => handleTimeSlotChange(index, 'start_time', time)}
              options={timesJSON}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Select
              placeholder="结束时间"
              value={timeSlot.end_time}
              onChange={(time) => handleTimeSlotChange(index, 'end_time', time)}
              options={timesJSON}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Button
              type="text"
              danger
              icon={<MinusCircleOutlined />}
              onClick={() => removeTimeSlot(index)}
              disabled={value.length === 1}
            >
              删除
            </Button>
          </Col>
        </Row>
      ))}
      <Button
        type="dashed"
        onClick={addTimeSlot}
        icon={<PlusOutlined />}
        style={{ width: '100%' }}
      >
        添加时间段
      </Button>
    </div>
  );
};

// Recurrence type options
const RecurrenceTypeOptions = [
  { value: 'daily', label: '每天重复' },
  { value: 'weekly', label: '每周重复' },
  { value: 'monthly', label: '每月重复' },
  { value: 'yearly', label: '每年重复' },
];

// Interval options for different recurrence types
const IntervalOptions = {
  daily: Array.from({ length: 30 }, (_, i) => ({ value: i + 1, label: `${i + 1}天` })),
  weekly: Array.from({ length: 12 }, (_, i) => ({ value: i + 1, label: `${i + 1}周` })),
  monthly: Array.from({ length: 12 }, (_, i) => ({ value: i + 1, label: `${i + 1}个月` })),
  yearly: Array.from({ length: 10 }, (_, i) => ({ value: i + 1, label: `${i + 1}年` })),
};

// End condition options
const EndConditionOptions = [
  { value: 'date', label: '结束日期' },
  { value: 'count', label: '重复次数' },
];

// Monthly repeat options
const MonthlyRepeatOptions = [
  { value: 'date', label: '按日期' },
  { value: 'weekday', label: '按星期' },
];

// Course name builder options
const SeasonOptions = [
  { value: '春季', label: '春季' },
  { value: '夏季', label: '夏季' },
  { value: '秋季', label: '秋季' },
  { value: '冬季', label: '冬季' },
];

const MonthOptions = Array.from({ length: 12 }, (_, i) => ({
  value: `${i + 1}月`,
  label: `${i + 1}月`,
}));

const OnlineOfflineOptions = [
  { value: '线上', label: '线上' },
  { value: '线下', label: '线下' },
];

// Helper function to get current season based on month
const getCurrentSeason = () => {
  const month = new Date().getMonth() + 1; // 1-12
  if (month >= 3 && month <= 5) return '春季';
  if (month >= 6 && month <= 8) return '夏季';
  if (month >= 9 && month <= 11) return '秋季';
  return '冬季';
};

// Helper function to get current month
const getCurrentMonth = () => {
  return `${new Date().getMonth() + 1}月`;
};

// Helper function to get current year
const getCurrentYear = () => {
  return `${new Date().getFullYear()}年`;
};

// Helper function to extract core content from course name
const extractCoreContent = (courseName: string): string => {
  if (!courseName) return '';

  // Remove common prefixes (year, season, month)
  let content = courseName;

  // Remove year prefix (e.g., "2024年")
  content = content.replace(/^\d{4}年/, '');

  // Remove season prefix (e.g., "春季", "夏季", "秋季", "冬季")
  content = content.replace(/^(春季|夏季|秋季|冬季)/, '');

  // Remove month prefix (e.g., "1月", "2月", etc.)
  content = content.replace(/^\d{1,2}月/, '');

  // Remove only standard online/offline suffixes (e.g., "-线上", "-线下")
  content = content.replace(/-(线上|线下)$/, '');

  return content.trim();
};

// Helper function to analyze course name structure
const analyzeCourseNameStructure = (courseName: string) => {
  if (!courseName) {
    return {
      yearEnabled: false,
      seasonEnabled: false,
      monthEnabled: false,
      onlineOfflineEnabled: false,
      selectedYear: getCurrentYear(),
      selectedSeason: getCurrentSeason(),
      selectedMonth: getCurrentMonth(),
      selectedOnlineOffline: '线上',
      coreName: ''
    };
  }

  let remainingName = courseName;

  // Check for year prefix
  const yearMatch = remainingName.match(/^(\d{4}年)/);
  const yearEnabled = !!yearMatch;
  const selectedYear = yearMatch ? yearMatch[1] : getCurrentYear();
  if (yearMatch) {
    remainingName = remainingName.replace(/^\d{4}年/, '');
  }

  // Check for season prefix
  const seasonMatch = remainingName.match(/^(春季|夏季|秋季|冬季)/);
  const seasonEnabled = !!seasonMatch;
  const selectedSeason = seasonMatch ? seasonMatch[1] : getCurrentSeason();
  if (seasonMatch) {
    remainingName = remainingName.replace(/^(春季|夏季|秋季|冬季)/, '');
  }

  // Check for month prefix
  const monthMatch = remainingName.match(/^(\d{1,2}月)/);
  const monthEnabled = !!monthMatch;
  const selectedMonth = monthMatch ? monthMatch[1] : getCurrentMonth();
  if (monthMatch) {
    remainingName = remainingName.replace(/^\d{1,2}月/, '');
  }

  // Check for standard online/offline suffix (only "-线上" or "-线下")
  const suffixMatch = remainingName.match(/-(线上|线下)$/);
  const onlineOfflineEnabled = !!suffixMatch;
  const selectedOnlineOffline = suffixMatch ? suffixMatch[1] : '线上';
  if (suffixMatch) {
    remainingName = remainingName.replace(/-(线上|线下)$/, '');
  }

  const coreName = remainingName.trim();

  return {
    yearEnabled,
    seasonEnabled,
    monthEnabled,
    onlineOfflineEnabled,
    selectedYear,
    selectedSeason,
    selectedMonth,
    selectedOnlineOffline,
    coreName
  };
};

interface DataSource extends API.CourseEvent {}

export interface TeachingResearchFormProps extends SKFormProps<DataSource> {
  options?: {};
}

// Course Name Builder Component
interface CourseNameBuilderProps {
  form: any;
  value?: string;
  onChange?: (value: string) => void;
  initialCoreContent?: string;
  courseNameStructure?: ReturnType<typeof analyzeCourseNameStructure>;
}

const CourseNameBuilder: React.FC<CourseNameBuilderProps> = ({ form, onChange, initialCoreContent, courseNameStructure }) => {
  const [yearEnabled, setYearEnabled] = useState(courseNameStructure?.yearEnabled || false);
  const [seasonEnabled, setSeasonEnabled] = useState(courseNameStructure?.seasonEnabled || false);
  const [monthEnabled, setMonthEnabled] = useState(courseNameStructure?.monthEnabled || false);
  const [onlineOfflineEnabled, setOnlineOfflineEnabled] = useState(courseNameStructure?.onlineOfflineEnabled || false);

  const [coreName, setCoreName] = useState(courseNameStructure?.coreName || initialCoreContent || '');
  const [selectedYear, setSelectedYear] = useState(courseNameStructure?.selectedYear || getCurrentYear());
  const [selectedSeason, setSelectedSeason] = useState(courseNameStructure?.selectedSeason || getCurrentSeason());
  const [selectedMonth, setSelectedMonth] = useState(courseNameStructure?.selectedMonth || getCurrentMonth());
  const [selectedOnlineOffline, setSelectedOnlineOffline] = useState(courseNameStructure?.selectedOnlineOffline || '线上');

  // Build the complete course name
  const buildCourseName = () => {
    let parts = [];

    // Add prefix components
    if (yearEnabled) parts.push(selectedYear);
    if (seasonEnabled) parts.push(selectedSeason);
    if (monthEnabled) parts.push(selectedMonth);

    // Add core name
    if (coreName.trim()) parts.push(coreName.trim());

    // Add suffix with dash separator
    if (onlineOfflineEnabled && selectedOnlineOffline) {
      const baseName = parts.join('');
      return baseName + (baseName ? '-' : '') + selectedOnlineOffline;
    }

    return parts.join('');
  };

  // Update component state when courseNameStructure changes
  useEffect(() => {
    if (courseNameStructure) {
      setYearEnabled(courseNameStructure.yearEnabled);
      setSeasonEnabled(courseNameStructure.seasonEnabled);
      setMonthEnabled(courseNameStructure.monthEnabled);
      setOnlineOfflineEnabled(courseNameStructure.onlineOfflineEnabled);
      setCoreName(courseNameStructure.coreName);
      setSelectedYear(courseNameStructure.selectedYear);
      setSelectedSeason(courseNameStructure.selectedSeason);
      setSelectedMonth(courseNameStructure.selectedMonth);
      setSelectedOnlineOffline(courseNameStructure.selectedOnlineOffline);
    } else if (initialCoreContent !== undefined) {
      setCoreName(initialCoreContent);
    }
  }, [courseNameStructure, initialCoreContent]);

  // Update form value when any component changes
  useEffect(() => {
    const newName = buildCourseName();
    onChange?.(newName);
    form?.setFieldsValue({ name: newName });
  }, [yearEnabled, seasonEnabled, monthEnabled, onlineOfflineEnabled, coreName, selectedYear, selectedSeason, selectedMonth, selectedOnlineOffline]);

  return (
    <div>
      <Row gutter={[8, 8]} style={{ marginBottom: 8 }}>
        <Col span={6}>
          <Space size={4}>
            <Checkbox
              checked={yearEnabled}
              onChange={(e) => setYearEnabled(e.target.checked)}
            />

              <Input
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                size="small"
                style={{ width: 80 }}
              />

          </Space>
        </Col>

        <Col span={6}>
          <Space size={4}>
            <Checkbox
              checked={seasonEnabled}
              onChange={(e) => setSeasonEnabled(e.target.checked)}
            />

              <Select
                value={selectedSeason}
                onChange={setSelectedSeason}
                options={SeasonOptions}
                size="small"
                style={{ width: 80 }}
              />

          </Space>
        </Col>

        <Col span={6}>
          <Space size={4}>
            <Checkbox
              checked={monthEnabled}
              onChange={(e) => setMonthEnabled(e.target.checked)}
            />

              <Select
                value={selectedMonth}
                onChange={setSelectedMonth}
                options={MonthOptions}
                size="small"
                style={{ width: 70 }}
              />

          </Space>
        </Col>

        <Col span={6}>
          <Space size={4}>
            <Checkbox
              checked={onlineOfflineEnabled}
              onChange={(e) => setOnlineOfflineEnabled(e.target.checked)}
            />

              <Select
                value={selectedOnlineOffline}
                onChange={setSelectedOnlineOffline}
                options={OnlineOfflineOptions}
                size="small"
                style={{ width: 70 }}
              />

          </Space>
        </Col>
      </Row>

      <Input
        value={coreName}
        onChange={(e) => setCoreName(e.target.value)}
        placeholder="课程内容"
        style={{ marginBottom: 8 }}
      />

      <div style={{
        padding: 6,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
        fontSize: 13,
        color: '#000'
      }}>
        {buildCourseName() || '课程名称预览...'}
      </div>
    </div>
  );
};

// Multiple Category Selection Component
interface MultipleCategorySelectProps {
  value?: string[][];
  onChange?: (value: string[][]) => void;
}

const MultipleCategorySelect: React.FC<MultipleCategorySelectProps> = ({ value = [[]], onChange }) => {
  // Ensure we always have at least one category selector
  const categories = value.length === 0 ? [[]] : value;

  const handleAdd = () => {
    const newValue = [...categories, []];
    onChange?.(newValue);
  };

  const handleRemove = (index: number) => {
    const newValue = categories.filter((_, i) => i !== index);
    onChange?.(newValue.length === 0 ? [[]] : newValue);
  };

  const handleCategoryChange = (index: number, categoryPath: string[]) => {
    const newValue = [...categories];
    newValue[index] = categoryPath;
    onChange?.(newValue);
  };

  return (
    <div>
      <Row gutter={[16, 8]}>
        {categories.map((categoryPath, index) => (
          <Col key={index} xs={24} sm={24} md={12} lg={12} xl={12}>
            <Space style={{ display: 'flex', width: '100%' }} align="baseline">
              <Cascader
                style={{ flex: 1, minWidth: 200 }}
                options={courseJSON.Category}
                value={categoryPath}
                onChange={(val) => handleCategoryChange(index, val as string[])}
                placeholder="选择课程属性"
              />
              {categories.length > 1 && (
                <MinusCircleOutlined
                  onClick={() => handleRemove(index)}
                  style={{ cursor: 'pointer', color: '#ff4d4f' }}
                />
              )}
            </Space>
          </Col>
        ))}
      </Row>
      <Row style={{ marginTop: 16 }}>
        <Col span={24}>
          <Button
            type="dashed"
            onClick={handleAdd}
            icon={<PlusOutlined />}
            style={{ width: '100%', maxWidth: 300 }}
          >
            添加课程属性
          </Button>
        </Col>
      </Row>
    </div>
  );
};

const TeachingResearchForm: React.FC<TeachingResearchFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic({
    ...props,
    onSubmit: (formData: any) => {
      // Transform the form data for multi-create type
      if (props?.type === 'multi-create') {
        const transformedData = transformRecurrenceData(formData);
        return props?.onSubmit?.(transformedData);
      } else {
        return props?.onSubmit?.(formData);
      }
    },
  });
  const { userOptions } = useUserOptions();
  const { zoomOptions } = useZoomOptions();
  const [extractedCoreContent, setExtractedCoreContent] = useState<string>('');
  const [courseNameStructure, setCourseNameStructure] = useState<ReturnType<typeof analyzeCourseNameStructure> | undefined>();

  // Helper function to compare time strings in HH:MM format
  const compareTimeStrings = (startTime: string, endTime: string): boolean => {
    if (!startTime || !endTime) return true; // Allow empty values

    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;

    return endMinutes > startMinutes;
  };

  // Custom validator for end time
  const validateEndTime = (startFieldName: string, errorMessage: string = '结束时间不能早于开始时间') => {
    return {
      validator: (_: any, value: any) => {
        if (!value) return Promise.resolve();

        const startTime = formProps?.form?.getFieldValue(startFieldName);
        if (!startTime) return Promise.resolve();

        if (compareTimeStrings(startTime, value)) {
          return Promise.resolve();
        }

        return Promise.reject(new Error(errorMessage));
      },
    };
  };

  // Custom validator for week times end (for multi-create form)
  const validateWeekEndTime = (dayIndex: number, errorMessage: string = '结束时间不能早于开始时间') => {
    return {
      validator: (_: any, value: any) => {
        if (!value) return Promise.resolve();

        const startTime = formProps?.form?.getFieldValue(['week_times_start', dayIndex]);
        if (!startTime) return Promise.resolve();

        if (compareTimeStrings(startTime, value)) {
          return Promise.resolve();
        }

        return Promise.reject(new Error(errorMessage));
      },
    };
  };

  // Helper function to normalize category data for backward compatibility
  const normalizeCategory = (category: any): string[][] => {
    if (!category) return [[]];

    // If it's already an array of arrays, return as is
    if (Array.isArray(category) && Array.isArray(category[0])) {
      return category;
    }

    // If it's a single array (old format), wrap it in an array
    if (Array.isArray(category)) {
      return [category];
    }

    // Default case
    return [[]];
  };

  // Custom validator for category field
  const validateCategories = {
    validator: (_: any, value: string[][]) => {
      if (!value || value.length === 0) {
        return Promise.reject(new Error('请至少选择一个课程属性'));
      }

      // Check if all category paths are complete (have at least 3 levels)
      const incompleteCategories = value.filter(categoryPath =>
        !categoryPath || categoryPath.length < 3 || categoryPath.some(item => !item)
      );

      if (incompleteCategories.length > 0) {
        return Promise.reject(new Error('请完整选择所有课程属性路径'));
      }

      return Promise.resolve();
    },
  };

  // Custom validator for end date
  const validateEndDate = {
    validator: (_: any, value: any) => {
      if (!value) return Promise.resolve();

      const startDate = formProps?.form?.getFieldValue('first_date');
      if (!startDate) return Promise.resolve();

      if (value.isAfter(startDate)) {
        return Promise.resolve();
      }

      return Promise.reject(new Error('结束日期必须晚于开始日期'));
    },
  };

  // Transform new recurrence data to bulk API format
  const transformRecurrenceData = (formData: any) => {
    const {
      recurrence_type,
      occurrence_count,
      end_condition,
      end_date,
      week_days,
      week_times_start,
      week_times_end,
      week_time_slots, // New: Enhanced multiple time slots per weekday
      advanced_time_mode, // New: Mode selector
      start_time,
      end_time,
      first_date,
      recurrence_interval,
      ...otherData
    } = formData;

    // Helper function to calculate duration in hours
    const calculateHours = (startTime: string, endTime: string): number => {
      if (!startTime || !endTime) return 1;

      const [startHour, startMin] = startTime.split(':').map(Number);
      const [endHour, endMin] = endTime.split(':').map(Number);

      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;

      return Math.max(0.5, (endMinutes - startMinutes) / 60);
    };

    // Generate course_dates array based on recurrence settings
    const course_dates: any[] = [];

    if (!first_date) {
      throw new Error('开始日期是必需的');
    }

    let currentDate = first_date.clone();
    const interval = recurrence_interval || 1;
    let sessionCount = 0;
    const maxSessions = 100; // Safety limit

    // Determine end condition
    let shouldContinue = true;
    const targetCount = occurrence_count || 1;

    // For monthly/yearly recurrence, always use count-based limiting
    // For other types: count-based ending uses targetCount, date-based ending uses maxSessions
    const isCountBased = end_condition === 'count' || recurrence_type === 'monthly' || recurrence_type === 'yearly';
    const countLimit = isCountBased ? targetCount : maxSessions;

    while (shouldContinue && sessionCount < maxSessions && sessionCount < countLimit) {
      if (recurrence_type === 'weekly' && week_days && week_days.length > 0) {
        // For weekly recurrence, generate sessions for each selected weekday
        const sortedWeekDays = [...week_days].sort((a, b) => a - b);

        for (const dayOfWeek of sortedWeekDays) {
          // Check count limit before processing each weekday (for count-based ending or monthly/yearly)
          if (isCountBased && sessionCount >= targetCount) {
            shouldContinue = false;
            break;
          }

          const sessionDate = currentDate.clone();

          // Adjust to the correct weekday
          const currentDayOfWeek = sessionDate.day();
          const daysToAdd = (dayOfWeek - currentDayOfWeek + 7) % 7;
          const adjustedSessionDate = sessionDate.add(daysToAdd, 'day');

          // Skip if this date is before the start date (for first iteration)
          if (adjustedSessionDate.isBefore(first_date, 'day')) {
            continue;
          }

          // Check end condition
          if (end_condition === 'date' && end_date && adjustedSessionDate.isAfter(end_date, 'day')) {
            shouldContinue = false;
            break;
          }

          // Enhanced: Handle multiple time slots per weekday
          if (advanced_time_mode && week_time_slots && week_time_slots[dayOfWeek]) {
            // Advanced mode: Multiple time slots for this weekday
            const timeSlots = week_time_slots[dayOfWeek];
            for (const timeSlot of timeSlots) {
              if (timeSlot.start_time && timeSlot.end_time) {
                course_dates.push({
                  date: adjustedSessionDate.format('YYYY-MM-DD'),
                  start_date_str: timeSlot.start_time,
                  end_date_str: timeSlot.end_time,
                  hours: calculateHours(timeSlot.start_time, timeSlot.end_time)
                });
                sessionCount++;

                // Check count limit after each time slot
                if (isCountBased && sessionCount >= targetCount) {
                  shouldContinue = false;
                  break;
                }
              }
            }
          } else {
            // Simple mode: Single time slot (legacy behavior)
            const startTime = week_times_start?.[dayOfWeek] || start_time || '09:00';
            const endTime = week_times_end?.[dayOfWeek] || end_time || '10:00';

            course_dates.push({
              date: adjustedSessionDate.format('YYYY-MM-DD'),
              start_date_str: startTime,
              end_date_str: endTime,
              hours: calculateHours(startTime, endTime)
            });
            sessionCount++;
          }

          // Break if count limit reached
          if (isCountBased && sessionCount >= targetCount) {
            shouldContinue = false;
            break;
          }
        }

        // Move to next week
        currentDate = currentDate.add(interval, 'week');
      } else {
        // For daily, monthly, yearly recurrence
        // Check end condition before creating session
        if (end_condition === 'date' && end_date && currentDate.isAfter(end_date, 'day')) {
          break;
        }

        const sessionStartTime = start_time || '09:00';
        const sessionEndTime = end_time || '10:00';

        course_dates.push({
          date: currentDate.format('YYYY-MM-DD'),
          start_date_str: sessionStartTime,
          end_date_str: sessionEndTime,
          hours: calculateHours(sessionStartTime, sessionEndTime)
        });

        sessionCount++;

        // Move to next occurrence AFTER creating the session
        switch (recurrence_type) {
          case 'daily':
            currentDate = currentDate.add(interval, 'day');
            break;
          case 'monthly':
            currentDate = currentDate.add(interval, 'month');
            break;
          case 'yearly':
            currentDate = currentDate.add(interval, 'year');
            break;
          default:
            shouldContinue = false;
        }
      }
    }

    // Ensure we have at least one course date
    if (course_dates.length === 0) {
      const defaultStartTime = start_time || '09:00';
      const defaultEndTime = end_time || '10:00';

      course_dates.push({
        date: first_date.format('YYYY-MM-DD'),
        start_date_str: defaultStartTime,
        end_date_str: defaultEndTime,
        hours: calculateHours(defaultStartTime, defaultEndTime)
      });
    }

    // Create recurrence rule object for storage and future duplication
    const recurrence_rule: API.RecurrenceRule = {
      recurrence_type,
      recurrence_interval,
      end_condition,
      occurrence_count,
      end_date: end_date?.format('YYYY-MM-DD'),
      week_days,

      // Enhanced weekly time slots support
      week_time_slots: advanced_time_mode ? week_time_slots : undefined,

      // Legacy single time slot support (for backward compatibility)
      start_time,
      end_time,
      week_times_start: !advanced_time_mode ? week_times_start : undefined,
      week_times_end: !advanced_time_mode ? week_times_end : undefined,

      first_date: first_date.format('YYYY-MM-DD')
    };

    const result = {
      ...otherData,
      course_dates,
      recurrence_rule // Include recurrence rule for storage and duplication
    };

    // Debug logging
    console.log('Original form data:', formData);
    console.log('Transformed data for bulk API:', result);
    console.log('Generated course dates:', course_dates);
    console.log('Recurrence rule:', recurrence_rule);
    console.log('Advanced time mode:', advanced_time_mode);
    console.log('Week time slots:', week_time_slots);
    console.log('Recurrence type:', recurrence_type, 'Target count:', targetCount, 'Interval:', interval);

    return result;
  };

  // init
  useEffect(() => {
    if (props?.visible) {
      if (props?.type === 'multi-create' && props?.dataSource) {
        // For duplicate functionality, exclude date-related fields and set defaults for multi-create
        const { date, _id, ...duplicateData } = props?.dataSource;
        // Analyze the course name structure for the CourseNameBuilder
        const structure = analyzeCourseNameStructure(duplicateData.name || '');
        const coreContent = extractCoreContent(duplicateData.name || '');
        setExtractedCoreContent(coreContent);
        setCourseNameStructure(structure);

        // Prepare form data with recurrence rules if available
        const formData: any = {
          ...duplicateData,
          category: normalizeCategory(duplicateData?.category), // Normalize category
        };

        // If recurrence_rule exists, populate the recurrence form fields with backward compatibility
        if (duplicateData.recurrence_rule) {
          const rule = duplicateData.recurrence_rule;
          formData.recurrence_type = rule.recurrence_type;
          formData.recurrence_interval = rule.recurrence_interval;
          formData.end_condition = rule.end_condition;
          formData.occurrence_count = rule.occurrence_count;
          formData.end_date = rule.end_date ? dayjs(rule.end_date) : undefined;
          formData.week_days = rule.week_days;
          formData.start_time = rule.start_time;
          formData.end_time = rule.end_time;
          formData.first_date = rule.first_date ? dayjs(rule.first_date) : undefined;

          // Enhanced weekly time slots support with backward compatibility
          if (rule.week_time_slots) {
            // New format: Multiple time slots per weekday
            formData.advanced_time_mode = true;
            formData.week_time_slots = rule.week_time_slots;
          } else if (rule.week_times_start && rule.week_times_end) {
            // Legacy format: Single time slot per weekday
            formData.advanced_time_mode = false;
            formData.week_times_start = rule.week_times_start;
            formData.week_times_end = rule.week_times_end;
          } else {
            // Very old format: Single time for all weekdays
            formData.advanced_time_mode = false;
          }
        } else {
          // Set default recurrence values for new duplications
          formData.recurrence_type = 'daily';
          formData.recurrence_interval = 1;
          formData.end_condition = 'count';
          formData.occurrence_count = 1;
          formData.advanced_time_mode = false;
        }

        formProps?.form?.setFieldsValue({
          ...formData,
          first_date: undefined, // Clear the start date for user input
          zoom_account: undefined, // Clear zoom account to allow auto-assignment
        });
      } else if (props?.type === 'edit' && props?.dataSource) {
        // For edit functionality, also analyze course name structure
        const structure = analyzeCourseNameStructure(props?.dataSource?.name || '');
        const coreContent = extractCoreContent(props?.dataSource?.name || '');
        setExtractedCoreContent(coreContent);
        setCourseNameStructure(structure);

        // Prepare form data for editing
        const editFormData: any = {
          ...props?.dataSource,
          category: normalizeCategory(props?.dataSource?.category), // Normalize category
          date: initFormDate(props?.dataSource?.date),
        };

        // If recurrence_rule exists, populate the recurrence form fields for editing with backward compatibility
        if (props?.dataSource?.recurrence_rule) {
          const rule = props?.dataSource?.recurrence_rule;
          editFormData.recurrence_type = rule.recurrence_type;
          editFormData.recurrence_interval = rule.recurrence_interval;
          editFormData.end_condition = rule.end_condition;
          editFormData.occurrence_count = rule.occurrence_count;
          editFormData.end_date = rule.end_date ? dayjs(rule.end_date) : undefined;
          editFormData.week_days = rule.week_days;
          editFormData.start_time = rule.start_time;
          editFormData.end_time = rule.end_time;
          editFormData.first_date = rule.first_date ? dayjs(rule.first_date) : undefined;

          // Enhanced weekly time slots support with backward compatibility
          if (rule.week_time_slots) {
            // New format: Multiple time slots per weekday
            editFormData.advanced_time_mode = true;
            editFormData.week_time_slots = rule.week_time_slots;
          } else if (rule.week_times_start && rule.week_times_end) {
            // Legacy format: Single time slot per weekday
            editFormData.advanced_time_mode = false;
            editFormData.week_times_start = rule.week_times_start;
            editFormData.week_times_end = rule.week_times_end;
          } else {
            // Very old format: Single time for all weekdays
            editFormData.advanced_time_mode = false;
          }
        }

        formProps?.form?.setFieldsValue(editFormData);
      } else {
        // For new creation, clear the extracted content and structure
        setExtractedCoreContent('');
        setCourseNameStructure(undefined);
        formProps?.form?.setFieldsValue({
          ...props?.dataSource,
          category: normalizeCategory(props?.dataSource?.category), // Normalize category
          date: initFormDate(props?.dataSource?.date),
        });
      }
    }
  }, [props]);

  if (props?.type === 'multi-update') {
    return (
      <Modal {...modalProps} width={900}>
        <Form
          name="TeachingResearchEditForm"
          labelAlign="left"
          {...formItemLayout}
          {...formProps}
        >
          <Divider>申报信息</Divider>

          <Form.Item label="负责人" name="check_user">
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="所属部⻔" name="department_ids">
            <Cascader options={salaryJSON.Departments} />
          </Form.Item>
          <Form.Item label="工作内容" name="work_content">
            <Input />
          </Form.Item>

          <Divider>课程信息</Divider>

          <Form.Item label="课程属性" name="category">
            <MultipleCategorySelect />
          </Form.Item>
          <Form.Item label="课程名称" name="name">
            <CourseNameBuilder
              form={formProps?.form}
              initialCoreContent={extractedCoreContent}
              courseNameStructure={courseNameStructure}
            />
          </Form.Item>
          <Form.Item label="讲师名称" name="teacher">
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="开课日期">
            <Space>
              <Form.Item noStyle name="start_date_str">
                <Select showSearch placeholder="开始时间" options={timesJSON} />
              </Form.Item>
              <span>~</span>
              <Form.Item
                noStyle
                name="end_date_str"
                dependencies={['start_date_str']}
                rules={[validateEndTime('start_date_str')]}
              >
                <Select showSearch placeholder="结束时间" options={timesJSON} />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item label="使用账号" name="zoom_account">
            <Select
              allowClear
              showSearch
              options={zoomOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="备注" name="note">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  if (props?.type === 'edit') {
    return (
      <Modal {...modalProps} width={900}>
        <Form
          name="TeachingResearchEditForm"
          labelAlign="left"
          {...formItemLayout}
          {...formProps}
        >
          <Divider>申报信息</Divider>

          <Form.Item label="负责人" name="check_user">
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="所属部⻔" name="department_ids">
            <Cascader options={salaryJSON.Departments} />
          </Form.Item>
          <Form.Item label="工作内容" name="work_content">
            <Input />
          </Form.Item>

          <Divider>课程信息</Divider>
          <Form.Item
            label="课程属性"
            name="category"
            rules={[validateCategories]}
          >
            <MultipleCategorySelect />
          </Form.Item>
          <Form.Item
            label="课程名称"
            name="name"
            rules={[{ required: true, message: '请输入课程名称' }]}
            validateTrigger="onSubmit"
          >
            <CourseNameBuilder
              form={formProps?.form}
              initialCoreContent={extractedCoreContent}
              courseNameStructure={courseNameStructure}
            />
          </Form.Item>
          <Form.Item
            label="讲师名称"
            name="teacher"
            rules={[{ required: true }]}
          >
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="开课日期" required>
            <Space>
              <Form.Item noStyle name="date" rules={[{ required: true }]}>
                <DatePicker style={{ width: 300 }} format="YYYY-MM-DD" />
              </Form.Item>
              <Form.Item
                noStyle
                name="start_date_str"
                rules={[{ required: true, message: '【开始时间】' }]}
              >
                <Select showSearch placeholder="开始时间" options={timesJSON} />
              </Form.Item>
              <span>~</span>
              <Form.Item
                noStyle
                name="end_date_str"
                dependencies={['start_date_str']}
                rules={[
                  { required: true, message: '【结束时间】' },
                  validateEndTime('start_date_str')
                ]}
              >
                <Select showSearch placeholder="结束时间" options={timesJSON} />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item label="使用账号" name="zoom_account">
            <Select
              allowClear
              showSearch
              options={zoomOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="备注" name="note">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  if (props?.type === 'multi-create') {
    return (
      <Modal {...modalProps} width={900}>
        <Form
          name="TeachingResearchForm"
          labelAlign="left"
          {...formItemLayout}
          {...formProps}
        >
          <Divider>申报信息</Divider>

          <Form.Item label="负责人" name="check_user">
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="所属部⻔" name="department_ids">
            <Cascader options={salaryJSON.Departments} />
          </Form.Item>
          <Form.Item label="工作内容" name="work_content">
            <Input />
          </Form.Item>

          <Divider>课程信息</Divider>
          <Form.Item
            label="课程属性"
            name="category"
            rules={[validateCategories]}
          >
            <MultipleCategorySelect />
          </Form.Item>
          <Form.Item
            label="课程名称"
            name="name"
            rules={[{ required: true, message: '请输入课程名称' }]}
            validateTrigger="onSubmit"
          >
            <CourseNameBuilder
              form={formProps?.form}
              initialCoreContent={extractedCoreContent}
              courseNameStructure={courseNameStructure}
            />
          </Form.Item>
          <Form.Item
            label="讲师名称"
            name="teacher"
            rules={[{ required: true }]}
          >
            <Select
              allowClear
              showSearch
              options={userOptions}
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="备注" name="note">
            <Input.TextArea />
          </Form.Item>
          <Divider>循环规则</Divider>

          {/* Start Date */}
          <Form.Item
            label="开始日期"
            name="first_date"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <DatePicker
              style={{ width: 300 }}
              format="YYYY-MM-DD"
              onChange={(date) => {
                if (date) {
                  const form = formProps?.form;
                  const recurrenceType = form?.getFieldValue('recurrence_type');

                  // Auto-select weekday for weekly recurrence
                  if (recurrenceType === 'weekly') {
                    const dayOfWeek = date.day();
                    form?.setFieldsValue({
                      week_days: [dayOfWeek]
                    });
                  }
                }
              }}
            />
          </Form.Item>

          {/* Recurrence Type */}
          <Form.Item
            label="重复类型"
            name="recurrence_type"
            rules={[{ required: true, message: '请选择重复类型' }]}
            initialValue="daily"
          >
            <Select
              style={{ width: 300 }}
              options={RecurrenceTypeOptions}
              onChange={(value) => {
                // Reset related fields when recurrence type changes
                const form = formProps?.form;
                if (form) {
                  const resetFields: any = {
                    recurrence_interval: 1,
                    week_days: undefined,
                    monthly_repeat_type: 'date',
                    end_condition: (value === 'monthly' || value === 'yearly') ? 'count' : 'count',
                    end_date: undefined,
                    occurrence_count: 1,
                    start_time: undefined,
                    end_time: undefined,
                  };

                  // For weekly recurrence, auto-select the day based on start date
                  if (value === 'weekly') {
                    const firstDate = form.getFieldValue('first_date');
                    if (firstDate) {
                      const dayOfWeek = firstDate.day();
                      resetFields.week_days = [dayOfWeek];
                    }
                  }

                  // For monthly/yearly, force end condition to count
                  if (value === 'monthly' || value === 'yearly') {
                    resetFields.end_condition = 'count';
                    resetFields.occurrence_count = 1;
                  }

                  form.setFieldsValue(resetFields);
                }
              }}
            />
          </Form.Item>

          {/* Dynamic fields based on recurrence type */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.recurrence_type !== curValues.recurrence_type
            }
          >
            {({ getFieldValue }) => {
              const recurrenceType = getFieldValue('recurrence_type');

              return (
                <>
                  {/* Interval Selection */}
                  <Form.Item
                    label="重复间隔"
                    name="recurrence_interval"
                    rules={[{ required: true, message: '请选择重复间隔' }]}
                    initialValue={1}
                  >
                    <Select
                      style={{ width: 300 }}
                      options={IntervalOptions[recurrenceType as keyof typeof IntervalOptions] || []}
                    />
                  </Form.Item>

                  {/* Weekly specific options */}
                  {recurrenceType === 'weekly' && (
                    <Form.Item
                      label="星期选择"
                      name="week_days"
                      rules={[{ required: true, message: '请选择星期' }]}
                    >
                      <Checkbox.Group options={CheckboxOptions} />
                    </Form.Item>
                  )}

                  {/* Monthly specific options - hidden for monthly/yearly */}
                  {recurrenceType === 'monthly' && false && (
                    <Form.Item
                      label="重复方式"
                      name="monthly_repeat_type"
                      initialValue="date"
                    >
                      <Select
                        style={{ width: 300 }}
                        options={MonthlyRepeatOptions}
                      />
                    </Form.Item>
                  )}
                </>
              );
            }}
          </Form.Item>

          {/* End Conditions - hidden for monthly/yearly */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.recurrence_type !== curValues.recurrence_type
            }
          >
            {({ getFieldValue }) => {
              const recurrenceType = getFieldValue('recurrence_type');

              // Hide end condition selector for monthly/yearly, auto-set to count
              if (recurrenceType === 'monthly' || recurrenceType === 'yearly') {
                return null;
              }

              return (
                <Form.Item
                  label="结束条件"
                  name="end_condition"
                  rules={[{ required: true, message: '请选择结束条件' }]}
                  initialValue="count"
                >
                  <Select
                    style={{ width: 300 }}
                    options={EndConditionOptions}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>

          {/* Dynamic end condition fields */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.end_condition !== curValues.end_condition ||
              prevValues.recurrence_type !== curValues.recurrence_type
            }
          >
            {({ getFieldValue }) => {
              const endCondition = getFieldValue('end_condition');
              const recurrenceType = getFieldValue('recurrence_type');

              // For monthly/yearly, always show occurrence count only
              if (recurrenceType === 'monthly' || recurrenceType === 'yearly') {
                return (
                  <Form.Item
                    label="重复次数"
                    name="occurrence_count"
                    rules={[{ required: true, message: '请输入重复次数' }]}
                    initialValue={1}
                  >
                    <InputNumber
                      min={1}
                      max={100}
                      style={{ width: 300 }}
                    />
                  </Form.Item>
                );
              }

              if (endCondition === 'date') {
                return (
                  <Form.Item
                    label="结束日期"
                    name="end_date"
                    dependencies={['first_date']}
                    rules={[
                      { required: true, message: '请选择结束日期' },
                      validateEndDate
                    ]}
                  >
                    <DatePicker
                      style={{ width: 300 }}
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                );
              }

              if (endCondition === 'count') {
                return (
                  <Form.Item
                    label="重复次数"
                    name="occurrence_count"
                    rules={[{ required: true, message: '请输入重复次数' }]}
                    initialValue={1}
                  >
                    <InputNumber
                      min={1}
                      max={100}
                      style={{ width: 300 }}
                    />
                  </Form.Item>
                );
              }

              return null;
            }}
          </Form.Item>

          {/* Time Selection for Selected Days */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.week_days !== curValues.week_days ||
              prevValues.recurrence_type !== curValues.recurrence_type
            }
          >
            {({ getFieldValue }) => {
              const recurrenceType = getFieldValue('recurrence_type');
              const dates = getFieldValue('week_days') || [];

              // Only show time selection for weekly recurrence or when specific days are selected
              if (recurrenceType !== 'weekly' || dates.length === 0) {
                // For non-weekly recurrence, show simple start/end time
                if (recurrenceType && recurrenceType !== 'none' && recurrenceType !== 'weekly') {
                  return (
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="开始时间"
                          name="start_time"
                          rules={[{ required: true, message: '请选择开始时间' }]}
                        >
                          <Select
                            showSearch
                            placeholder="开始时间"
                            options={timesJSON}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="结束时间"
                          name="end_time"
                          dependencies={['start_time']}
                          rules={[
                            { required: true, message: '请选择结束时间' },
                            validateEndTime('start_time')
                          ]}
                        >
                          <Select
                            showSearch
                            placeholder="结束时间"
                            options={timesJSON}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  );
                }
                return null;
              }

              // Weekly recurrence with selected days - Enhanced with multiple time slots support
              return (
                <div>
                  {/* Toggle between simple and advanced mode */}
                  <Form.Item
                    label="时间设置模式"
                    name="advanced_time_mode"
                    initialValue={false}
                  >
                    <Select
                      style={{ width: 200 }}
                      options={[
                        { value: false, label: '简单模式 (统一时间)' },
                        { value: true, label: '高级模式 (每日独立时间)' }
                      ]}
                    />
                  </Form.Item>

                  {/* Dynamic time slot management based on mode */}
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.advanced_time_mode !== curValues.advanced_time_mode
                    }
                  >
                    {({ getFieldValue }) => {
                      const advancedMode = getFieldValue('advanced_time_mode');

                      if (advancedMode) {
                        // Advanced mode: Multiple time slots per weekday
                        return (
                          <Form.Item
                            label="每日时间安排"
                            name="week_time_slots"
                          >
                            <div>
                              {dates.map((date: any) => (
                                <WeeklyTimeSlotManager
                                  key={date}
                                  weekday={date}
                                  weekdayLabel={CheckboxOptions[date]?.label}
                                  value={getFieldValue('week_time_slots')?.[date] || [{ start_time: '', end_time: '' }]}
                                  onChange={(timeSlots) => {
                                    const form = formProps?.form;
                                    if (form) {
                                      const currentSlots = form.getFieldValue('week_time_slots') || {};
                                      form.setFieldsValue({
                                        week_time_slots: {
                                          ...currentSlots,
                                          [date]: timeSlots
                                        }
                                      });
                                    }
                                  }}
                                />
                              ))}
                            </div>
                          </Form.Item>
                        );
                      } else {
                        // Simple mode: Single time slot for all days (legacy behavior)
                        return (
                          <Row>
                            {dates.map((date: any, index: any) => (
                              <Col key={index} span={8}>
                                <Form.Item
                                  label={`${CheckboxOptions[date]?.label}`}
                                  style={{ marginBottom: 0 }}
                                  required
                                >
                                  <Space>
                                    <Form.Item
                                      name={['week_times_start', date]}
                                      rules={[
                                        { required: true, message: '【开始时间】' },
                                      ]}
                                    >
                                      <Select
                                        showSearch
                                        placeholder="开始时间"
                                        options={timesJSON}
                                        onChange={(value) => {
                                          // Only propagate from first selected day to others
                                          const form = formProps?.form;
                                          if (form && index === 0 && value) {
                                            const currentWeekTimes = form.getFieldValue('week_times_start') || {};
                                            const updatedTimes = { ...currentWeekTimes };

                                            // Copy start time to all other selected days
                                            dates.forEach((otherDate: any, otherIndex: any) => {
                                              if (otherIndex !== 0) {
                                                updatedTimes[otherDate] = value;
                                              }
                                            });

                                            form.setFieldsValue({
                                              week_times_start: updatedTimes
                                            });
                                          }
                                        }}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      name={['week_times_end', date]}
                                      dependencies={[['week_times_start', date]]}
                                      rules={[
                                        { required: true, message: '【结束时间】' },
                                        validateWeekEndTime(date)
                                      ]}
                                    >
                                      <Select
                                        showSearch
                                        placeholder="结束时间"
                                        options={timesJSON}
                                        onChange={(value) => {
                                          // Only propagate from first selected day to others
                                          const form = formProps?.form;
                                          if (form && index === 0 && value) {
                                            const currentWeekTimes = form.getFieldValue('week_times_end') || {};
                                            const updatedTimes = { ...currentWeekTimes };

                                            // Copy end time to all other selected days
                                            dates.forEach((otherDate: any, otherIndex: any) => {
                                              if (otherIndex !== 0) {
                                                updatedTimes[otherDate] = value;
                                              }
                                            });

                                            form.setFieldsValue({
                                              week_times_end: updatedTimes
                                            });
                                          }
                                        }}
                                      />
                                    </Form.Item>
                                  </Space>
                                </Form.Item>
                              </Col>
                            ))}
                          </Row>
                        );
                      }
                    }}
                  </Form.Item>
                </div>
              );
            }}
          </Form.Item>
        </Form>
      </Modal>
    );
  }

  return <></>;
};

export default TeachingResearchForm;
