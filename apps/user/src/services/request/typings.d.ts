// @ts-ignore
/* eslint-disable */

declare namespace API {
  type ID = string | number;
  type IMAGE = string;
  type TODO = {
    [key: string]: any;
  };
  type OPTION = {
    value: string | number;
    label: string;
    [key: string]: any;
  };
  type Ref<T> = any | T;
  type PagiNation<T> = {
    page?: number;
    perPage?: number;
    sortField?: string;
    sortOrder?: 1 | -1;
  } & Partial<T>;
  type BankAccount = {
    _id: ID;
    account_type: number; //⼯资收款账户类型: 1⽇本银⾏账户2中国银⾏账户3⽀付宝
    bank_name: string; //银⾏名
    bank_branch_name: string; //店名
    account_no: string; //⼝座番号
    holder_name: string; //⼝座名义
    is_self: boolean; //收款账户是否为本⼈
    payee_name: string; //收款⼈姓名
    bank_account_image: IMAGE; //银⾏账户上图片
    bank_account_images: IMAGE[]; //银⾏账户上图片
    CN_ID_no: string; //收款⼈⼆代居⺠身份证号
    CN_ID_image: IMAGE; //收款⼈⼆代居⺠身份证图片
    CN_ID_images: IMAGE[]; //收款⼈⼆代居⺠身份证图片
  };
  // 单个时间段接口
  interface TimeSlot {
    start_time: string; // HH:mm format
    end_time: string; // HH:mm format
  }

  // 重复规则接口
  interface RecurrenceRule {
    recurrence_type: 'daily' | 'weekly' | 'monthly' | 'yearly';
    recurrence_interval: number;
    end_condition: 'count' | 'date';
    occurrence_count?: number;
    end_date?: string; // YYYY-MM-DD format
    week_days?: number[]; // For weekly recurrence [0-6, where 0=Sunday]

    // Enhanced weekly time slots - supports multiple time slots per weekday
    week_time_slots?: { [weekday: number]: TimeSlot[] }; // New: Multiple time slots per weekday

    // Legacy single time slot support (for backward compatibility)
    start_time?: string; // HH:mm format - deprecated, use week_time_slots for weekly
    end_time?: string; // HH:mm format - deprecated, use week_time_slots for weekly
    week_times_start?: { [weekday: number]: string }; // Legacy format
    week_times_end?: { [weekday: number]: string }; // Legacy format

    first_date: string; // YYYY-MM-DD format
  }

  type CourseEvent = {
    _id: API.ID;
    // 课程属性 - 支持多个类别路径
    category: string[][];
    // 课程状态：0未完成1已完成
    status: number;
    // 课程名称
    name: string;
    // 讲师姓名
    teacher?: Ref<User>;
    // 上课日期
    date: Date;
    // 开始时间
    start_date_str: string;
    // 结束时间
    end_date_str: string;
    // 持续时间
    hours: number;
    // zoom账号
    zoom_account: string;
    // 备注
    note: string;
    // 提醒通知状态
    has_notification: boolean;
    // 重复规则 - 用于复制功能
    recurrence_rule?: RecurrenceRule;
  };
  type User = {
    _id: ID;
    status: number; //在职状态 0:未入职 1:已入职 2:已离职
    expiryDate?: string;
    email: string;
    password: string;
    oldPassword: string;
    newPassword: string;
    last_name_cn: string;
    last_name_en: string;
    last_name_jp: string;
    first_name_cn: string;
    first_name_en: string;
    first_name_jp: string;
    full_name_cn: string;
    birthday: string;
    gender: number; //1:male 2:female
    tel: string; //
    address: string; //现居住地
    ID_type: number; //身份证件类型: 1在留卡2⼆代居⺠身份证3护照
    nationality: string; //国籍
    ID_no: string; //证件号
    ID_expiry_date: Date; //证件有效期限
    ID_license: string; //在留资格
    is_activity_permission: boolean; //资格外活动许可有无
    is_dependents: boolean; //抚养有无
    my_number: string; //个⼈番号
    address_of_license: string; //证件上地址所在地
    ID_image: IMAGE; //证件图片
    ID_images: IMAGE[]; //证件图片
    graduate_university: string; //毕业院校名
    graduate_date: string; //毕业时间
    faculty: string; //专业
    can_courses: string; //可带课程
    degree: string; //学位
    bank_account: BankAccount;
    department_ids: [number]; //所属部门
    role: number; // 0:超级用户 1:管理用户 2:普通用户
  };
  type SalaryRecord = {
    _id: ID;

    // 申报人
    user?: ID;

    // 申报姓名
    user_fullname_cn?: string;

    // 审核人
    check_user?: ID;

    // 小助手
    sub_check_user?: ID;

    // 1未提交 2待审核 3审核通过 4审核驳回
    apply_status?: number;

    // 1审核中 2待定 3通过 4驳回
    check_status?: number;

    // 1转让审核中 2转让审核待定 3转让审核通过 4转让审核驳回
    sub_check_status?: number;

    // 审核时间
    check_date: Date;

    // 工作日期
    work_date: Date;

    // 部门
    department_ids: number[];

    // 申报类型: 1时长 2件数 3字数 4仅交通费 5定给 6其他
    apply_type: number;

    // 单价
    salary_per: number;

    // 开始时间
    start_time: Date;

    start_time_str: string;

    // 结束时间
    end_time: Date;

    end_time_str: string;

    // 人数/件数/字数
    amount: number;

    // 交通费起点
    travel_start: string;

    // 交通费终点
    travel_end: string;

    // 交通费
    travel_fee: number;

    // 劳动时间
    work_hours: number;

    // 休息时间
    rest_hours: number;

    // 此项工作收入额
    final_salary: number;

    // 工作内容
    work_content: string;

    // 备注文件
    file_links: IMAGE[];

    // 备注
    memo: string;
  };
  type SalaryMonth = {
    _id: API.ID;
    user: User;
    check_user?: API.ID;
    full_name_cn: string;
    account_type: number;
    bank_name: string;
    bank_branch_name: string;
    account_no: string;
    holder_name: string;
    year: string;
    month: string;
    base_amount: number;
    travel_fee: number;
    total_amount: number;
    department_amount: any[];
    checkUserSalary?: [
      {
        check_user: API.ID;
        base_amount: number;
        travel_fee: number;
        total_amount: number;
        department_amount: any[];
      },
    ];
  };
  type SalaryRecordStat = {
    _id: {
      year: number;
      month: number;
    };
    undoneCount: number;
    count: number;
  };
  type Student = {
    _id: API.ID;
    // 签约状态：1咨询中2签约中3已签约(暂定初始2，新建付款时变成3并且on_status=2)
    sign_status: number;
    // 在学状态：1签约中2在学中3冻结中4即将到期（怎么判定=>最晚的报名课程一个月开始）5已到期（怎么判定=>最晚的报名课程到期）6已退费 7 其他
    on_status: number;
    // 学生属性 1学部2大学院3其他
    student_type: number;
    // 具体vip记录在新model里面，有个确认功能。[日付年月日date、開始時間string、終了時間string]
    // memo: vip跟申报记录进行关联，vip -> salary_record 单向同步。
    is_vip_course: boolean;
    vip_total_hours: number;
    vip_used_hours: number;
    // 第1销售第2销售
    // 只有1个班主任
    // ------------------------咨询阶段------------------------
    name: string;
    // 第1销售
    sale_teacher1?: Ref<User>;
    // 第2销售
    sale_teacher2?: Ref<User>;
    // 微信号
    wechat: string;
    // 身份证号
    CN_ID_no: string;
    // 在留卡号
    JP_ID_no: string;
    // 毕业院校
    grad_univ: string;
    // 毕业院校类别
    grad_univ_category: string;
    grad_faculty: string;
    goal_univ: string;
    goal_faculty: string;
    // 来日 1已定2未定
    is_to_jp: boolean;
    // 来日时间
    to_jp_date: Date;
    is_lang_school: boolean;
    lang_school: string;
    // 英语成绩
    en_scores: Score[];
    // 日语成绩
    ja_scores: Score[];
    courses: Course[];
    // 绩点
    is_gpa: boolean;
    gpa_score: string;
    // 咨询备注
    consult_note: string;
    // 考学规划
    plan_contents: string;
    // ------------------------签约时阶段------------------------
    // 签约日期： 录入第一条付款记录，老师点击已签约时，自动生成。
    sign_date: Date;
    // 综合课程有效期
    sign_expired_date: Date;
    // 编号顺序根据年份reset；第一次点击生成合同时确定，若无sign_date就生成sign_date(永久固定)，二次点击不会再更改了；新建student.contract_max_no（最后4位），倒序，然后+1来确定最后4位
    contract_no: string;
    // 契约条款备注
    contract_note: string;
    tel_jp: string;
    tel_cn: string;
    address: string;
    emergency_name: string;
    emergency_relationship: string;
    emergency_tel: string;
    emergency_wechat: string;
    // ------------------------付款阶段------------------------
    // 数组
    payment_histories: PaymentHistory[];
    // ------------------------后期跟进阶段------------------------
    // 班主任
    current_follow_teacher?: Ref<User>;
    // 班主任转移怎么设计 => 当前只有1个   跟进数据怎么设计？ => 数组
    // -------------------------------------------------------------
    follow_histories: FollowHistory[];
    // -------------------------------------------------------------
    // 合格院校
    pass_univ: string;
  };
  type VipCourse = {
    student: Ref<Student>;
    // 上课老师
    vip_teacher?: Ref<User>;
    // 上课日期
    vip_date: Date;
    // 开始时间
    start_date_str: string;
    // 结束时间
    end_date_str: string;
    // 讲义时间
    duration: number;
    // 备注
    note: string;
  };
  type ZoomSetting = {
    _id: API.ID;
    account: string;
  };

  // 子文档
  type Course = {
    _id: API.ID;
    // 报名学院 => 选择式
    department_name: string;
    // （例：情报基础，数学，英语）
    course_name: string;
    is_ja_course: boolean;
    ja_course_note: string;
    is_en_course: boolean;
    en_course_note: string;
    is_math_course: boolean;
    math_course_note: string;
    vip_course: string;
    original_price: number;
    off_price: number;
    // 人民币和日元怎么区分？=> 日元
    final_price: number;
    custom_source: string;
    expired_date: Date;
    other_note: string;
    // 销售老师
    apply_teacher?: Ref<User>;
    // 受理人
    accept_teacher?: Ref<User>;
    // 课程月份；加一个createdAt和updatedAt
    course_month: number;
  };
  type PaymentHistory = {
    _id: API.ID;
    type: string;
    amount: number;
    method: string;
    date: Date;
    note: string;
  };
  type FollowHistory = {
    _id: API.ID;
    category: string;
    content: string;
    files: string[];
    teacher: Ref<User>;
  };
  type Score = {
    category: string;
    score: string;
  };
}
